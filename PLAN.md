# Plan for Reaching MVP

This document outlines the phased plan to transform the current frontend prototype into a fully functional and secure Minimum Viable Product (MVP). The core focus is on implementing a robust backend with Supabase, securing all data with Row Level Security (RLS), and completing the essential user journeys.

---

### Phase 1: Backend Foundation & Security

This is the most critical phase. We will establish the database structure and lock it down with security rules *before* writing the application code that interacts with it.

#### [x] 1. Database Schema Design & Migration

We will create the following tables in Supabase. The `user_id` and `organization_id` fields will be crucial for our security policies.

```mermaid
erDiagram
    organizations {
        uuid id PK "Primary Key"
        text name "Organization Name"
        timestamp created_at
    }

    profiles {
        uuid id PK "Foreign Key to auth.users"
        uuid organization_id FK "Links to organizations.id"
        text full_name
        text email
        text role "admin, trainer, or candidate"
        timestamp created_at
    }

    questions {
        uuid id PK
        uuid organization_id FK "Links to organizations.id"
        text question_text
        jsonb options "Array of strings"
        integer correct_answer "Index of correct option"
        text subject
        text chapter
        text difficulty "easy, medium, hard"
        text explanation
        timestamp created_at
    }

    tests {
        uuid id PK
        uuid organization_id FK "Links to organizations.id"
        text title
        timestamp created_at
    }

    test_questions {
        uuid test_id PK, FK "Links to tests.id"
        uuid question_id PK, FK "Links to questions.id"
    }

    test_results {
        uuid id PK
        uuid test_id FK "Links to tests.id"
        uuid user_id FK "Candidate's user ID"
        integer score
        jsonb answers "User's answers"
        timestamp created_at
    }

    organizations ||--o{ profiles : "has"
    organizations ||--o{ questions : "has"
    organizations ||--o{ tests : "has"
    profiles }|..|{ test_results : "takes"
    tests ||--o{ test_questions : "contains"
    questions ||--o{ test_questions : "is part of"
    tests ||--o{ test_results : "has"

```

#### [x] 2. Security: Row Level Security (RLS)

We will enable Row Level Security on all tables to ensure users can only access data they are permitted to see. This is the most important step for security.

*   **`profiles` Table:**
    *   Users can view their own profile.
    *   Admins can view profiles within their own organization.
*   **`organizations` Table:**
    *   Users can only view the organization they belong to.
*   **`questions` & `tests` Tables:**
    *   Users with `admin` or `trainer` roles can create, view, update, and delete questions/tests within their own organization.
    *   Candidates cannot access these tables directly.
*   **`test_results` Table:**
    *   Candidates can view their own test results.
    *   Admins and trainers can view results for all users within their organization.

---

### [ ] Phase 2: Implement Core Features

With the backend secured, we'll connect the frontend components.

1.  **[x] Refine Authentication:**
    *   Modify the `SignUpForm.tsx` to create a new organization and link the new user to it.
2.  **[x] Connect Question Bank:**
    *   Implement full CRUD (Create, Read, Update, Delete) functionality in `QuestionBank.tsx`, replacing the local state with Supabase API calls.
3.  **[ ] Connect Dashboard:**
    *   Replace all hardcoded data in `Dashboard.tsx` with real data fetched from Supabase, respecting the user's role.

---

### [ ] Phase 3: Build Out User Journeys

1.  **[ ] Test Creation (Admin/Trainer):**
    *   Create a new component for building tests.
    *   This interface will allow trainers to select questions from the `QuestionBank` and save them as a new entry in the `tests` table.
2.  **[ ] Test Taking (Candidate):**
    *   Build out the `TestInterface.tsx` to fetch a test's questions, display them to the candidate, and record their answers in the `test_results` table upon submission.

---

### [ ] Phase 4: Frontend Polish

1.  **[ ] Implement Routing:**
    *   Install `react-router-dom`.
    *   Refactor `App.tsx` to use routes for each page, enabling direct URL access and browser navigation.