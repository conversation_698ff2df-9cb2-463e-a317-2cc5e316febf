import React, { useEffect, useState } from 'react';
import { Toaster } from 'react-hot-toast';
import { supabase } from './lib/supabase';
import { useAuthStore } from './stores/authStore';
import { LandingPage } from './components/landing/LandingPage';
import { LoginForm } from './components/auth/LoginForm';
import { SignUpForm } from './components/auth/SignUpForm';
import { DashboardLayout } from './components/dashboard/DashboardLayout';
import { Dashboard } from './components/dashboard/Dashboard';
import { QuestionBank } from './components/questions/QuestionBank';
import { TrainingMode } from './components/training/TrainingMode';
import { TestInterface } from './components/test/TestInterface';
import { Button } from './components/ui/Button';

function App() {
  const { user, profile, loading, setUser } = useAuthStore();
  const [showAuth, setShowAuth] = useState(false);
  const [showSignUp, setShowSignUp] = useState(false);
  const [currentPage, setCurrentPage] = useState('dashboard');

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null);
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user ?? null);
    });

    return () => subscription.unsubscribe();
  }, [setUser]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Show landing page if not authenticated and not showing auth forms
  if (!user && !showAuth) {
    return (
      <>
        <LandingPage onGetStarted={() => setShowAuth(true)} />
        <Toaster position="top-right" />
      </>
    );
  }

  // Show auth forms if not authenticated but auth is requested
  if (!user && showAuth) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
            {showSignUp ? (
              <SignUpForm onSwitchToLogin={() => setShowSignUp(false)} />
            ) : (
              <LoginForm onSwitchToSignUp={() => setShowSignUp(true)} />
            )}
          <div className="text-center mt-4">
            <Button
              variant="ghost"
              onClick={() => setShowAuth(false)}
            >
              ← Back to Home
            </Button>
          </div>
        </div>
        <Toaster position="top-right" />
      </div>
    );
  }

  // If user is logged in but profile is still loading, show a loader
  if (user && !profile) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p className="ml-4 text-lg text-gray-600">Loading your profile...</p>
      </div>
    );
  }

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'dashboard':
        return <Dashboard />;
      case 'questions':
        return <QuestionBank />;
      case 'training':
        return <TrainingMode />;
      case 'take-test':
        return <TestInterface />;
      default:
        return <Dashboard />;
    }
  };

  return (
    <div className="min-h-screen">
      <DashboardLayout currentPage={currentPage} onPageChange={setCurrentPage}>
        {renderCurrentPage()}
      </DashboardLayout>
      <Toaster position="top-right" />
    </div>
  );
}

export default App;