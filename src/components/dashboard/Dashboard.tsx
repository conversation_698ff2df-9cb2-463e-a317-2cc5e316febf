import React from 'react';
import { motion } from 'framer-motion';
import { BarChart3, Users, FileText, Clock, TrendingUp, Award, Plus, ArrowRight, Target, BookOpen } from 'lucide-react';
import { useAuthStore } from '../../stores/authStore';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { BoltBadge } from '../ui/BoltBadge';

export const Dashboard: React.FC = () => {
  const { profile } = useAuthStore();

  const getStatsForRole = () => {
    switch (profile?.role) {
      case 'admin':
        return [
          { title: 'Total Users', value: '1,234', icon: Users, color: 'blue', change: '+12%' },
          { title: 'Active Tests', value: '45', icon: FileText, color: 'green', change: '+8%' },
          { title: 'Questions Bank', value: '2,856', icon: BarChart3, color: 'purple', change: '+24%' },
          { title: 'Avg. Score', value: '78%', icon: Award, color: 'yellow', change: '+3%' },
        ];
      case 'trainer':
        return [
          { title: 'My Tests', value: '12', icon: FileText, color: 'blue', change: '+2' },
          { title: 'Questions Created', value: '234', icon: BarChart3, color: 'green', change: '+18' },
          { title: 'Students Trained', value: '156', icon: Users, color: 'purple', change: '+12' },
          { title: 'Avg. Performance', value: '82%', icon: TrendingUp, color: 'yellow', change: '+5%' },
        ];
      case 'candidate':
        return [
          { title: 'Tests Taken', value: '8', icon: FileText, color: 'blue', change: '+2' },
          { title: 'Avg. Score', value: '85%', icon: Award, color: 'green', change: '+7%' },
          { title: 'Time Spent', value: '24h', icon: Clock, color: 'purple', change: '+3h' },
          { title: 'Rank', value: '#12', icon: TrendingUp, color: 'yellow', change: '+3' },
        ];
      default:
        return [];
    }
  };

  const stats = getStatsForRole();

  const colorClasses = {
    blue: 'from-blue-500 to-blue-600',
    green: 'from-emerald-500 to-emerald-600',
    purple: 'from-purple-500 to-purple-600',
    yellow: 'from-yellow-500 to-yellow-600',
  };

  const quickActions = {
    admin: [
      { title: 'Add New User', icon: Users, action: 'users' },
      { title: 'Create Test', icon: FileText, action: 'tests' },
      { title: 'View Analytics', icon: BarChart3, action: 'analytics' },
      { title: 'Manage Questions', icon: BookOpen, action: 'questions' },
    ],
    trainer: [
      { title: 'Create Test', icon: FileText, action: 'tests' },
      { title: 'Add Questions', icon: Plus, action: 'questions' },
      { title: 'Training Mode', icon: Target, action: 'training' },
      { title: 'View Results', icon: BarChart3, action: 'analytics' },
    ],
    candidate: [
      { title: 'Take Test', icon: FileText, action: 'take-test' },
      { title: 'View Results', icon: Award, action: 'results' },
      { title: 'Practice Mode', icon: Target, action: 'practice' },
      { title: 'Study Materials', icon: BookOpen, action: 'materials' },
    ],
  };

  const recentActivities = {
    admin: [
      { action: 'New user registered', subject: 'John Doe joined as Trainer', time: '2 hours ago', type: 'success' },
      { action: 'Test completed', subject: 'Mathematics Quiz by 15 candidates', time: '4 hours ago', type: 'info' },
      { action: 'Question bank updated', subject: '25 new questions added', time: '6 hours ago', type: 'primary' },
      { action: 'System backup', subject: 'Daily backup completed', time: '1 day ago', type: 'warning' },
    ],
    trainer: [
      { action: 'Test created', subject: 'Physics Chapter 5 Quiz', time: '1 hour ago', type: 'success' },
      { action: 'Questions added', subject: '10 new physics questions', time: '3 hours ago', type: 'info' },
      { action: 'Training session', subject: 'Completed with 12 students', time: '5 hours ago', type: 'primary' },
      { action: 'Test results', subject: 'Chemistry test graded', time: '1 day ago', type: 'warning' },
    ],
    candidate: [
      { action: 'Test completed', subject: 'Mathematics Quiz - Score: 85%', time: '2 hours ago', type: 'success' },
      { action: 'Practice session', subject: 'Physics problems - 30 minutes', time: '4 hours ago', type: 'info' },
      { action: 'Achievement unlocked', subject: 'Completed 5 tests this week', time: '6 hours ago', type: 'primary' },
      { action: 'Study reminder', subject: 'Chemistry test tomorrow', time: '1 day ago', type: 'warning' },
    ],
  };

  const currentQuickActions = quickActions[profile?.role || 'candidate'];
  const currentActivities = recentActivities[profile?.role || 'candidate'];

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-8 text-white relative overflow-hidden">
        <div className="flex items-center justify-between relative z-10">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              Welcome back, {profile?.full_name}!
            </h1>
            <p className="text-blue-100 text-lg">
              {profile?.role === 'candidate' 
                ? "Ready to continue your learning journey?" 
                : `Here's what's happening with your ${profile?.role === 'admin' ? 'platform' : 'training'} today.`
              }
            </p>
          </div>
          <div className="hidden md:block">
            <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center">
              <span className="text-3xl font-bold">
                {profile?.full_name?.charAt(0).toUpperCase()}
              </span>
            </div>
          </div>
        </div>
        
        {/* Bolt Badge in Welcome Section */}
        <div className="absolute bottom-4 right-4">
          <BoltBadge variant="inline" className="bg-white/20 border-white/30 text-white hover:bg-white/30" />
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card hover className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className={`p-3 rounded-xl bg-gradient-to-r ${colorClasses[stat.color as keyof typeof colorClasses]} shadow-lg`}>
                    <Icon className="h-6 w-6 text-white" />
                  </div>
                  <span className="text-sm font-medium text-green-600 bg-green-100 px-2 py-1 rounded-full">
                    {stat.change}
                  </span>
                </div>
                <div>
                  <p className="text-sm text-gray-600 mb-1">{stat.title}</p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                </div>
              </Card>
            </motion.div>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Quick Actions */}
        <div className="lg:col-span-1">
          <Card className="p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Quick Actions</h2>
            <div className="space-y-3">
              {currentQuickActions.map((action, index) => {
                const Icon = action.icon;
                return (
                  <motion.button
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="w-full flex items-center p-3 bg-gray-50 hover:bg-blue-50 rounded-lg transition-all duration-200 group"
                  >
                    <div className="p-2 bg-blue-100 group-hover:bg-blue-200 rounded-lg mr-3">
                      <Icon className="h-5 w-5 text-blue-600" />
                    </div>
                    <span className="font-medium text-gray-900 group-hover:text-blue-600">
                      {action.title}
                    </span>
                    <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-blue-600 ml-auto" />
                  </motion.button>
                );
              })}
            </div>
          </Card>
        </div>

        {/* Recent Activity */}
        <div className="lg:col-span-2">
          <Card className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900">Recent Activity</h2>
              <Button variant="ghost" size="sm">
                View All
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
            <div className="space-y-4">
              {currentActivities.map((activity, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-start p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className={`w-3 h-3 rounded-full mt-2 mr-4 ${
                    activity.type === 'success' ? 'bg-green-500' :
                    activity.type === 'info' ? 'bg-blue-500' :
                    activity.type === 'primary' ? 'bg-purple-500' :
                    'bg-yellow-500'
                  }`} />
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">{activity.action}</p>
                    <p className="text-sm text-gray-600">{activity.subject}</p>
                  </div>
                  <span className="text-sm text-gray-500 whitespace-nowrap">{activity.time}</span>
                </motion.div>
              ))}
            </div>
          </Card>
        </div>
      </div>

      {/* Performance Chart Placeholder */}
      {(profile?.role === 'admin' || profile?.role === 'trainer') && (
        <Card className="p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Performance Overview</h2>
          <div className="h-64 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <BarChart3 className="h-12 w-12 text-blue-400 mx-auto mb-4" />
              <p className="text-gray-600">Performance charts coming soon</p>
              <p className="text-sm text-gray-500">Advanced analytics and reporting features</p>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};