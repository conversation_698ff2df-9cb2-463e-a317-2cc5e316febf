import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Menu, 
  X, 
  Home, 
  BookOpen, 
  FileText, 
  Users, 
  BarChart3, 
  Settings,
  LogOut,
  GraduationCap,
  Shield
} from 'lucide-react';
import { useAuthStore } from '../../stores/authStore';
import { Button } from '../ui/Button';

interface DashboardLayoutProps {
  children: React.ReactNode;
  currentPage: string;
  onPageChange: (page: string) => void;
}

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({ 
  children, 
  currentPage, 
  onPageChange 
}) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { profile, signOut } = useAuthStore();

  // Trainers get admin capabilities
  const isAdminOrTrainer = profile?.role === 'admin' || profile?.role === 'trainer';
  const isCandidate = profile?.role === 'candidate';

  const navigation = [
    { name: 'Dashboard', id: 'dashboard', icon: Home, roles: ['admin', 'trainer', 'candidate'] },
    { name: 'Question Bank', id: 'questions', icon: BookOpen, roles: ['admin', 'trainer'] },
    { name: 'Tests', id: 'tests', icon: FileText, roles: ['admin', 'trainer'] },
    { name: 'Take Test', id: 'take-test', icon: GraduationCap, roles: ['candidate'] },
    { name: 'Training Mode', id: 'training', icon: BookOpen, roles: ['trainer'] },
    { name: 'Manage Users', id: 'users', icon: Users, roles: ['admin', 'trainer'] },
    { name: 'Analytics', id: 'analytics', icon: BarChart3, roles: ['admin', 'trainer'] },
    { name: 'Settings', id: 'settings', icon: Settings, roles: ['admin', 'trainer', 'candidate'] },
  ];

  const filteredNavigation = navigation.filter(item => 
    item.roles.includes(profile?.role || 'candidate')
  );

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const getRoleDisplay = (role: string) => {
    if (role === 'trainer') return 'Trainer (Admin)';
    return role.charAt(0).toUpperCase() + role.slice(1);
  };

  const getRoleIcon = (role: string) => {
    if (role === 'trainer') return <Shield className="h-4 w-4 text-blue-500" />;
    if (role === 'admin') return <Shield className="h-4 w-4 text-purple-500" />;
    return <GraduationCap className="h-4 w-4 text-green-500" />;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Mobile sidebar */}
      <motion.div
        initial={false}
        animate={{ x: sidebarOpen ? 0 : '-100%' }}
        className="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl lg:hidden"
      >
        <div className="flex items-center justify-between p-4 border-b">
          <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">TestMatic</h1>
          <button onClick={() => setSidebarOpen(false)}>
            <X className="h-6 w-6 text-gray-500" />
          </button>
        </div>
        <SidebarContent 
          navigation={filteredNavigation} 
          currentPage={currentPage} 
          onPageChange={onPageChange}
          profile={profile}
          onSignOut={handleSignOut}
          getRoleDisplay={getRoleDisplay}
          getRoleIcon={getRoleIcon}
        />
      </motion.div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex flex-col flex-grow bg-white shadow-xl">
          <div className="flex items-center p-6 border-b">
            <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              TestMatic
            </h1>
          </div>
          <SidebarContent 
            navigation={filteredNavigation} 
            currentPage={currentPage} 
            onPageChange={onPageChange}
            profile={profile}
            onSignOut={handleSignOut}
            getRoleDisplay={getRoleDisplay}
            getRoleIcon={getRoleIcon}
          />
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <div className="sticky top-0 z-40 bg-white/80 backdrop-blur-lg border-b border-gray-200">
          <div className="flex items-center justify-between px-4 py-4 sm:px-6">
            <button
              onClick={() => setSidebarOpen(true)}
              className="lg:hidden text-gray-500 hover:text-gray-700"
            >
              <Menu className="h-6 w-6" />
            </button>
            
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900">{profile?.full_name}</p>
                <div className="flex items-center justify-end space-x-1">
                  {getRoleIcon(profile?.role || 'candidate')}
                  <p className="text-xs text-gray-500">{getRoleDisplay(profile?.role || 'candidate')}</p>
                </div>
              </div>
              <div className="h-8 w-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center text-white font-medium">
                {profile?.full_name?.charAt(0).toUpperCase()}
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="p-6">
          {children}
        </main>
      </div>

      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
};

const SidebarContent: React.FC<{
  navigation: any[];
  currentPage: string;
  onPageChange: (page: string) => void;
  profile: any;
  onSignOut: () => void;
  getRoleDisplay: (role: string) => string;
  getRoleIcon: (role: string) => React.ReactNode;
}> = ({ navigation, currentPage, onPageChange, profile, onSignOut, getRoleDisplay, getRoleIcon }) => {
  return (
    <div className="flex flex-col flex-grow">
      {/* User info */}
      <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
        <div className="flex items-center space-x-3">
          <div className="h-10 w-10 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center text-white font-medium">
            {profile?.full_name?.charAt(0).toUpperCase()}
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">{profile?.full_name}</p>
            <div className="flex items-center space-x-1">
              {getRoleIcon(profile?.role || 'candidate')}
              <p className="text-xs text-gray-500">{getRoleDisplay(profile?.role || 'candidate')}</p>
            </div>
          </div>
        </div>
      </div>

      <nav className="flex-1 px-4 py-6 space-y-2">
        {navigation.map((item) => {
          const Icon = item.icon;
          const isActive = currentPage === item.id;
          
          return (
            <motion.button
              key={item.id}
              whileHover={{ x: 4 }}
              onClick={() => onPageChange(item.id)}
              className={`
                flex items-center w-full px-3 py-2 rounded-lg text-left transition-all duration-200
                ${isActive 
                  ? 'bg-gradient-to-r from-blue-500 to-indigo-500 text-white shadow-lg' 
                  : 'text-gray-700 hover:bg-gray-100'
                }
              `}
            >
              <Icon className="h-5 w-5 mr-3" />
              {item.name}
            </motion.button>
          );
        })}
      </nav>

      <div className="p-4 border-t">
        <Button
          variant="ghost"
          onClick={onSignOut}
          className="w-full justify-start text-red-600 hover:bg-red-50"
        >
          <LogOut className="h-5 w-5 mr-3" />
          Sign Out
        </Button>
      </div>
    </div>
  );
};