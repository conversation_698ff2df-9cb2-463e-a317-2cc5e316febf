import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  BookOpen, 
  Users, 
  BarChart3, 
  Zap, 
  Globe,
  ArrowRight,
  Play,
  Star,
  Target,
  Lightbulb,
  Clock,
  TrendingUp,
  DollarSign,
  ThumbsUp,
  Menu,
  X,
  CheckCircle
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { Modal } from '../ui/Modal';
import { BoltBadge } from '../ui/BoltBadge';

interface LandingPageProps {
  onGetStarted: () => void;
}

export const LandingPage: React.FC<LandingPageProps> = ({ onGetStarted }) => {
  const [isDemoModalOpen, setDemoModalOpen] = useState(false);
  const [isMobileMenuOpen, setMobileMenuOpen] = useState(false);

  const features = [
    {
      icon: BookOpen,
      title: 'Interactive Training Mode',
      description: 'Animated MCQ reveals with drawing capabilities for visual explanations and enhanced learning',
      color: 'from-blue-500 to-blue-600'
    },
    {
      icon: Target,
      title: 'Smart Test Engine',
      description: 'Advanced time tracking, question marking, and intelligent analytics for comprehensive assessment',
      color: 'from-emerald-500 to-emerald-600'
    },
    {
      icon: BarChart3,
      title: 'Advanced Analytics',
      description: 'Detailed performance insights, progress tracking, and data-driven learning recommendations',
      color: 'from-purple-500 to-purple-600'
    },
    {
      icon: Users,
      title: 'Multi-Role Support',
      description: 'Admin, Trainer, and Candidate roles with tailored experiences and permission management',
      color: 'from-orange-500 to-orange-600'
    },
    {
      icon: Globe,
      title: 'SAAS Ready',
      description: 'Multi-tenant architecture designed for organizations of any size with enterprise security',
      color: 'from-indigo-500 to-indigo-600'
    },
    {
      icon: Zap,
      title: 'Easy CSV Import',
      description: 'Bulk upload questions with subjects, chapters, difficulty levels, and automated validation',
      color: 'from-yellow-500 to-yellow-600'
    }
  ];

  const benefits = [
    { text: 'Reduce training time by 40%', icon: Clock, value: '40%' },
    { text: 'Improve test scores by 35%', icon: TrendingUp, value: '35%' },
    { text: 'Save 60% on training costs', icon: DollarSign, value: '60%' },
    { text: 'Increase engagement by 50%', icon: ThumbsUp, value: '50%' }
  ];

  const testimonials = [
    {
      name: 'Sarah Johnson',
      role: 'Training Manager',
      company: 'TechCorp',
      content: 'TestMatic transformed our training program. The interactive features keep our trainees engaged and the analytics help us track progress effectively.',
      rating: 5,
      avatar: 'SJ'
    },
    {
      name: 'Michael Chen',
      role: 'Education Director',
      company: 'EduInstitute',
      content: 'The analytics help us identify knowledge gaps instantly. Game-changer for our assessments with detailed insights and reporting.',
      rating: 5,
      avatar: 'MC'
    },
    {
      name: 'Lisa Rodriguez',
      role: 'HR Director',
      company: 'GlobalTech',
      content: 'Easy to use, powerful features, and excellent support. The multi-role system works perfectly for our organization. Highly recommended!',
      rating: 5,
      avatar: 'LR'
    }
  ];

  const pricingPlans = [
    {
      name: 'Starter',
      price: '$29',
      period: '/month',
      description: 'Perfect for small teams',
      features: [
        'Up to 50 questions',
        '5 active tests',
        'Basic analytics',
        'Email support',
        '10 candidates'
      ],
      popular: false
    },
    {
      name: 'Professional',
      price: '$79',
      period: '/month',
      description: 'Best for growing organizations',
      features: [
        'Unlimited questions',
        'Unlimited tests',
        'Advanced analytics',
        'Priority support',
        '100 candidates',
        'Custom branding',
        'API access'
      ],
      popular: true
    },
    {
      name: 'Enterprise',
      price: 'Custom',
      period: '',
      description: 'For large organizations',
      features: [
        'Everything in Professional',
        'Unlimited candidates',
        'SSO integration',
        'Dedicated support',
        'Custom integrations',
        'SLA guarantee'
      ],
      popular: false
    }
  ];

  const scrollToSection = (id: string) => {
    const section = document.getElementById(id);
    if (section) {
      section.scrollIntoView({ behavior: 'smooth' });
    }
    setMobileMenuOpen(false);
  };

  return (
    <>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        {/* Header */}
        <header className="sticky top-0 z-20 bg-white/90 backdrop-blur-lg border-b border-white/20 shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                    TestMatic
                  </h1>
                </div>
              </div>
              
              {/* Desktop Navigation */}
              <div className="hidden md:flex items-center space-x-6">
                <Button variant="ghost" onClick={() => scrollToSection('features')}>Features</Button>
                <Button variant="ghost" onClick={() => scrollToSection('pricing')}>Pricing</Button>
                <Button variant="ghost" onClick={() => scrollToSection('testimonials')}>Testimonials</Button>
                <Button variant="ghost" onClick={() => scrollToSection('footer')}>About</Button>
                <Button onClick={onGetStarted} className="ml-4">Get Started</Button>
              </div>

              {/* Mobile menu button */}
              <div className="md:hidden">
                <Button variant="ghost" onClick={() => setMobileMenuOpen(!isMobileMenuOpen)}>
                  {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
                </Button>
              </div>
            </div>

            {/* Mobile Navigation */}
            {isMobileMenuOpen && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="md:hidden border-t border-gray-200 py-4"
              >
                <div className="flex flex-col space-y-3">
                  <Button variant="ghost" onClick={() => scrollToSection('features')} className="justify-start">Features</Button>
                  <Button variant="ghost" onClick={() => scrollToSection('pricing')} className="justify-start">Pricing</Button>
                  <Button variant="ghost" onClick={() => scrollToSection('testimonials')} className="justify-start">Testimonials</Button>
                  <Button variant="ghost" onClick={() => scrollToSection('footer')} className="justify-start">About</Button>
                  <Button onClick={onGetStarted} className="mt-4">Get Started</Button>
                </div>
              </motion.div>
            )}
          </div>
        </header>

        {/* Hero Section */}
        <section className="relative py-20 lg:py-32 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
              >
                <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-6">
                  <Star className="h-4 w-4 mr-2" />
                  Trusted by 10,000+ organizations worldwide
                </div>
                <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
                  <span className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent">
                    Transform
                  </span>
                  <br />
                  <span className="text-gray-900">Your Training</span>
                </h1>
                <p className="text-xl md:text-2xl text-gray-600 mb-8 max-w-4xl mx-auto leading-relaxed">
                  Professional test creation and assessment platform with interactive teaching tools, 
                  advanced analytics, and seamless candidate experience for modern education.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="flex flex-col sm:flex-row gap-4 justify-center mb-12"
              >
                <Button size="lg" onClick={onGetStarted} className="text-lg px-8 py-4 shadow-lg">
                  <Play className="h-5 w-5 mr-2" />
                  Start Free Trial
                </Button>
                <Button variant="outline" size="lg" className="text-lg px-8 py-4" onClick={() => setDemoModalOpen(true)}>
                  Watch Demo
                  <ArrowRight className="h-5 w-5 ml-2" />
                </Button>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-5xl mx-auto"
              >
                {benefits.map((benefit, index) => {
                  const Icon = benefit.icon;
                  return (
                    <div key={index} className="text-center">
                      <div className="flex justify-center items-center mb-3">
                        <div className="p-3 bg-blue-100 rounded-full">
                          <Icon className="h-6 w-6 text-blue-600" />
                        </div>
                      </div>
                      <div className="text-3xl font-bold text-blue-600 mb-1">
                        {benefit.value}
                      </div>
                      <div className="text-sm text-gray-600">
                        {benefit.text.replace(benefit.value, '').trim()}
                      </div>
                    </div>
                  )
                })}
              </motion.div>
            </div>
          </div>

          {/* Background Elements */}
          <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
            <div className="absolute top-20 left-10 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
            <div className="absolute top-40 right-10 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse animation-delay-2000"></div>
            <div className="absolute bottom-20 left-1/2 w-72 h-72 bg-indigo-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse animation-delay-4000"></div>
          </div>
        </section>

        {/* Features Section */}
        <div id="features">
          <section className="py-20 bg-white/70 backdrop-blur-sm">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-16">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8 }}
                  viewport={{ once: true }}
                >
                  <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                    Everything You Need for
                    <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent"> Modern Training</span>
                  </h2>
                  <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                    Powerful features designed to enhance learning outcomes and streamline assessment processes
                  </p>
                </motion.div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {features.map((feature, index) => {
                  const Icon = feature.icon;
                  return (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.8, delay: index * 0.1 }}
                      viewport={{ once: true }}
                    >
                      <Card hover className="p-8 h-full border-2 border-transparent hover:border-blue-200 transition-all duration-300">
                        <div className={`w-14 h-14 rounded-xl bg-gradient-to-r ${feature.color} flex items-center justify-center mb-6 shadow-lg`}>
                          <Icon className="h-7 w-7 text-white" />
                        </div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">
                          {feature.title}
                        </h3>
                        <p className="text-gray-600 leading-relaxed">
                          {feature.description}
                        </p>
                      </Card>
                    </motion.div>
                  );
                })}
              </div>
            </div>
          </section>
        </div>

        {/* How It Works Section */}
        <section className="py-20 bg-gradient-to-r from-gray-50 to-blue-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                  Simple Process,
                  <span className="bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent"> Powerful Results</span>
                </h2>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                  Get started in minutes with our intuitive three-step process
                </p>
              </motion.div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  step: '01',
                  title: 'Upload Questions',
                  description: 'Import your question bank via CSV with subjects, chapters, and difficulty levels. Our smart validation ensures data integrity.',
                  icon: BookOpen
                },
                {
                  step: '02',
                  title: 'Create & Train',
                  description: 'Use interactive training mode with animated reveals and drawing tools. Engage learners with visual explanations.',
                  icon: Lightbulb
                },
                {
                  step: '03',
                  title: 'Test & Analyze',
                  description: 'Conduct assessments with real-time tracking and detailed analytics. Get insights to improve learning outcomes.',
                  icon: TrendingUp
                }
              ].map((item, index) => {
                const Icon = item.icon;
                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: index * 0.2 }}
                    viewport={{ once: true }}
                    className="text-center"
                  >
                    <div className="relative mb-8">
                      <div className="w-24 h-24 mx-auto bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center shadow-xl">
                        <Icon className="h-10 w-10 text-white" />
                      </div>
                      <div className="absolute -top-2 -right-2 w-10 h-10 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-full flex items-center justify-center text-white text-lg font-bold shadow-lg">
                        {item.step}
                      </div>
                    </div>
                    <h3 className="text-2xl font-semibold text-gray-900 mb-4">
                      {item.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed text-lg">
                      {item.description}
                    </p>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <div id="pricing">
          <section className="py-20 bg-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-16">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8 }}
                  viewport={{ once: true }}
                >
                  <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                    Choose Your
                    <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent"> Perfect Plan</span>
                  </h2>
                  <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                    Flexible pricing options to fit organizations of all sizes
                  </p>
                </motion.div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {pricingPlans.map((plan, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <Card className={`p-8 h-full relative ${plan.popular ? 'border-2 border-blue-500 shadow-xl' : ''}`}>
                      {plan.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white px-4 py-2 rounded-full text-sm font-medium">
                            Most Popular
                          </span>
                        </div>
                      )}
                      <div className="text-center mb-8">
                        <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                        <p className="text-gray-600 mb-4">{plan.description}</p>
                        <div className="flex items-baseline justify-center">
                          <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                          <span className="text-gray-600 ml-1">{plan.period}</span>
                        </div>
                      </div>
                      <ul className="space-y-4 mb-8">
                        {plan.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-center">
                            <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                            <span className="text-gray-600">{feature}</span>
                          </li>
                        ))}
                      </ul>
                      <Button 
                        className={`w-full ${plan.popular ? 'bg-gradient-to-r from-blue-500 to-indigo-500' : ''}`}
                        variant={plan.popular ? 'primary' : 'outline'}
                        onClick={onGetStarted}
                      >
                        {plan.name === 'Enterprise' ? 'Contact Sales' : 'Start Free Trial'}
                      </Button>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </div>
          </section>
        </div>

        {/* Testimonials Section */}
        <div id="testimonials">
          <section className="py-20 bg-gradient-to-r from-blue-50 to-indigo-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-16">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8 }}
                  viewport={{ once: true }}
                >
                  <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                    Trusted by
                    <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent"> Training Professionals</span>
                  </h2>
                  <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                    See what our customers say about their experience with TestMatic
                  </p>
                </motion.div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {testimonials.map((testimonial, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <Card className="p-8 h-full">
                      <div className="flex items-center mb-4">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                        ))}
                      </div>
                      <p className="text-gray-600 mb-6 leading-relaxed text-lg">
                        "{testimonial.content}"
                      </p>
                      <div className="flex items-center">
                        <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                          {testimonial.avatar}
                        </div>
                        <div>
                          <div className="font-semibold text-gray-900">{testimonial.name}</div>
                          <div className="text-sm text-gray-500">{testimonial.role}, {testimonial.company}</div>
                        </div>
                      </div>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </div>
          </section>
        </div>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-blue-600 to-indigo-600 relative overflow-hidden">
          <div className="absolute inset-0 bg-black opacity-10"></div>
          <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8 relative z-10">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
                Ready to Transform Your Training?
              </h2>
              <p className="text-xl text-blue-100 mb-8 leading-relaxed max-w-3xl mx-auto">
                Join thousands of organizations already using TestMatic to deliver exceptional training experiences. 
                Start your free trial today and see the difference.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button 
                  size="lg" 
                  onClick={onGetStarted}
                  className="bg-white text-blue-600 hover:bg-gray-50 text-lg px-8 py-4 shadow-xl"
                >
                  Start Your Free Trial
                  <ArrowRight className="h-5 w-5 ml-2" />
                </Button>
                <Button 
                  variant="outline" 
                  size="lg"
                  onClick={() => setDemoModalOpen(true)}
                  className="border-white text-white hover:bg-white hover:text-blue-600 text-lg px-8 py-4"
                >
                  Schedule Demo
                </Button>
              </div>
              <p className="text-blue-200 text-sm mt-4">No credit card required • 14-day free trial</p>
            </motion.div>
          </div>
        </section>

        {/* Footer */}
        <div id="footer">
          <footer className="bg-gray-900 text-white py-16">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
                <div className="md:col-span-1">
                  <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-indigo-400 bg-clip-text text-transparent mb-4">
                    TestMatic
                  </h3>
                  <p className="text-gray-400 leading-relaxed mb-4">
                    Professional test creation and assessment platform for modern education and training.
                  </p>
                  <div className="flex space-x-4">
                    <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center hover:bg-gray-600 transition-colors cursor-pointer">
                      <span className="text-xs">f</span>
                    </div>
                    <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center hover:bg-gray-600 transition-colors cursor-pointer">
                      <span className="text-xs">t</span>
                    </div>
                    <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center hover:bg-gray-600 transition-colors cursor-pointer">
                      <span className="text-xs">in</span>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold mb-4 text-lg">Product</h4>
                  <ul className="space-y-3 text-gray-400">
                    <li><a className="hover:text-white transition-colors cursor-pointer" onClick={() => scrollToSection('features')}>Features</a></li>
                    <li><a className="hover:text-white transition-colors cursor-pointer" onClick={() => scrollToSection('pricing')}>Pricing</a></li>
                    <li><a className="hover:text-white transition-colors cursor-pointer">Security</a></li>
                    <li><a className="hover:text-white transition-colors cursor-pointer">Integrations</a></li>
                    <li><a className="hover:text-white transition-colors cursor-pointer">API</a></li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-4 text-lg">Company</h4>
                  <ul className="space-y-3 text-gray-400">
                    <li><a className="hover:text-white transition-colors cursor-pointer">About</a></li>
                    <li><a className="hover:text-white transition-colors cursor-pointer">Blog</a></li>
                    <li><a className="hover:text-white transition-colors cursor-pointer">Careers</a></li>
                    <li><a className="hover:text-white transition-colors cursor-pointer">Contact</a></li>
                    <li><a className="hover:text-white transition-colors cursor-pointer">Press</a></li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-4 text-lg">Support</h4>
                  <ul className="space-y-3 text-gray-400">
                    <li><a className="hover:text-white transition-colors cursor-pointer">Help Center</a></li>
                    <li><a className="hover:text-white transition-colors cursor-pointer">Documentation</a></li>
                    <li><a className="hover:text-white transition-colors cursor-pointer">API Reference</a></li>
                    <li><a className="hover:text-white transition-colors cursor-pointer">Status</a></li>
                    <li><a className="hover:text-white transition-colors cursor-pointer">Community</a></li>
                  </ul>
                </div>
              </div>
              <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p className="text-gray-400 text-sm">&copy; 2025 TestMatic. All rights reserved.</p>
                <div className="flex items-center space-x-6 mt-4 md:mt-0">
                  <BoltBadge variant="footer" />
                  <a className="text-gray-400 hover:text-white text-sm transition-colors cursor-pointer">Privacy Policy</a>
                  <a className="text-gray-400 hover:text-white text-sm transition-colors cursor-pointer">Terms of Service</a>
                  <a className="text-gray-400 hover:text-white text-sm transition-colors cursor-pointer">Cookie Policy</a>
                </div>
              </div>
            </div>
          </footer>
        </div>
      </div>

      {/* Floating Bolt Badge */}
      <BoltBadge variant="floating" />

      <Modal 
        isOpen={isDemoModalOpen} 
        onClose={() => setDemoModalOpen(false)} 
        title="TestMatic Demo"
        size="xl"
      >
        <div className="aspect-video bg-gray-100 rounded-lg flex items-center justify-center mb-4">
          <div className="text-center">
            <Play className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Demo video coming soon</p>
          </div>
        </div>
        <p className="text-gray-600">
          This demo showcases the powerful features of TestMatic, including the interactive training mode,
          smart test engine, and advanced analytics. See how you can transform your training process today.
        </p>
        <div className="mt-6 flex justify-end">
          <Button onClick={onGetStarted}>
            Start Free Trial
          </Button>
        </div>
      </Modal>
    </>
  );
};