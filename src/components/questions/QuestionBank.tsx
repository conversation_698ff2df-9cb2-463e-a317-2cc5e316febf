import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { Upload, Plus, Search, Filter, Trash2, Edit, Loader2 } from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { Input } from '../ui/Input';
import { Modal } from '../ui/Modal';
import toast from 'react-hot-toast';
import { useQuestionStore, Question } from '../../stores/questionStore';
import { QuestionForm, QuestionFormData } from './QuestionForm';
import { useAuthStore } from '../../stores/authStore';

export const QuestionBank: React.FC = () => {
  const {
    questions,
    loading,
    fetchQuestions,
    addQuestion,
    updateQuestion,
    deleteQuestion,
    uploadQuestions,
  } = useQuestionStore();
  const organizationId = useAuthStore((state) => state.profile?.organization_id);

  const [filteredQuestions, setFilteredQuestions] = useState<Question[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSubject, setSelectedSubject] = useState('');
  const [selectedDifficulty, setSelectedDifficulty] = useState('');
  
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showFormModal, setShowFormModal] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState<Question | null>(null);

  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    fetchQuestions();
  }, [fetchQuestions]);

  useEffect(() => {
    let filtered = questions;

    if (searchTerm) {
      const lowercasedTerm = searchTerm.toLowerCase();
      filtered = filtered.filter(q => 
        q.question_text.toLowerCase().includes(lowercasedTerm) ||
        q.subject.toLowerCase().includes(lowercasedTerm) ||
        (q.chapter && q.chapter.toLowerCase().includes(lowercasedTerm))
      );
    }

    if (selectedSubject) {
      filtered = filtered.filter(q => q.subject === selectedSubject);
    }

    if (selectedDifficulty) {
      filtered = filtered.filter(q => q.difficulty === selectedDifficulty);
    }

    setFilteredQuestions(filtered);
  }, [searchTerm, selectedSubject, selectedDifficulty, questions]);

  const subjects = [...new Set(questions.map(q => q.subject))];
  const difficulties = ['easy', 'medium', 'hard'];

  const handleAddClick = () => {
    setEditingQuestion(null);
    setShowFormModal(true);
  };

  const handleEditClick = (question: Question) => {
    setEditingQuestion(question);
    setShowFormModal(true);
  };

  const handleDeleteClick = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this question?')) {
      try {
        await deleteQuestion(id);
        toast.success('Question deleted successfully.');
      } catch (e) {
        const error = e instanceof Error ? e : new Error('An unknown error occurred');
        toast.error(`Failed to delete question: ${error.message}`);
      }
    }
  };

  const handleFormSubmit = async (data: QuestionFormData) => {
    if (!organizationId) {
      toast.error("Cannot determine organization. Please sign in again.");
      return;
    }
    
    try {
      if (editingQuestion) {
        await updateQuestion(editingQuestion.id, data);
        toast.success('Question updated successfully!');
      } else {
        await addQuestion({ ...data, organization_id: organizationId });
        toast.success('Question added successfully!');
      }
      setShowFormModal(false);
      setEditingQuestion(null);
    } catch (e) {
      const error = e instanceof Error ? e : new Error('An unknown error occurred');
      toast.error(error.message || 'Failed to save question.');
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !organizationId) return;

    const toastId = toast.loading('Parsing CSV file...');
    try {
      const Papa = await import('papaparse');
      const text = await file.text();
      
      Papa.parse(text, {
        header: true,
        skipEmptyLines: true,
        complete: async (results: import('papaparse').ParseResult<Record<string, string>>) => {
          toast.dismiss(toastId);
          const newQuestions = results.data.map((row) => ({
            organization_id: organizationId,
            question_text: row.question || row.Question || '',
            options: [
              row.option1 || row.Option1,
              row.option2 || row.Option2,
              row.option3 || row.Option3,
              row.option4 || row.Option4,
            ].filter(Boolean),
            correct_answer: parseInt(row.correct_answer || row.CorrectAnswer || '1') - 1,
            subject: row.subject || row.Subject || 'General',
            chapter: row.chapter || row.Chapter || 'Chapter 1',
            difficulty: (row.difficulty || row.Difficulty || 'medium').toLowerCase() as 'easy' | 'medium' | 'hard',
            explanation: row.explanation || row.Explanation,
          }));

          if (newQuestions.length > 0) {
            toast.loading('Uploading questions to the database...');
            await uploadQuestions(newQuestions);
            toast.dismiss();
            toast.success(`${newQuestions.length} questions imported successfully!`);
          } else {
            toast.error('No valid questions found in the CSV.');
          }
          setShowUploadModal(false);
        },
        error: (err: Error) => {
          const error = err;
          toast.dismiss(toastId);
          toast.error(`Error parsing CSV file: ${error.message}`);
          console.error(error);
        }
      });
    } catch (e) {
      const error = e instanceof Error ? e : new Error('An unknown error occurred');
      toast.dismiss(toastId);
      toast.error(`Error reading file: ${error.message}`);
      console.error(error);
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Question Bank</h1>
          <p className="text-gray-600 mt-2">Manage your test questions and import from CSV</p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline" onClick={() => setShowUploadModal(true)}>
            <Upload className="h-4 w-4 mr-2" />
            Import CSV
          </Button>
          <Button onClick={handleAddClick}>
            <Plus className="h-4 w-4 mr-2" />
            Add Question
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search questions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <select
            value={selectedSubject}
            onChange={(e) => setSelectedSubject(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Subjects</option>
            {subjects.map(subject => (
              <option key={subject} value={subject}>{subject}</option>
            ))}
          </select>

          <select
            value={selectedDifficulty}
            onChange={(e) => setSelectedDifficulty(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Difficulties</option>
            {difficulties.map(difficulty => (
              <option key={difficulty} value={difficulty} className="capitalize">
                {difficulty}
              </option>
            ))}
          </select>

          <Button variant="outline" disabled>
            <Filter className="h-4 w-4 mr-2" />
            Advanced Filter
          </Button>
        </div>
      </Card>

      {/* Question List */}
      <div className="grid gap-4">
        {loading && questions.length === 0 ? (
          <div className="flex justify-center items-center p-12">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          </div>
        ) : filteredQuestions.length === 0 ? (
          <Card className="p-12 text-center">
            <div className="max-w-md mx-auto">
              <div className="bg-gray-100 rounded-full p-3 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <Search className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Questions Found</h3>
              <p className="text-gray-500 mb-6">Your search or filter criteria did not match any questions. Try adjusting your search or add a new question.</p>
              <Button onClick={handleAddClick}>
                Add Question
              </Button>
            </div>
          </Card>
        ) : (
          filteredQuestions.map((question, index) => (
            <motion.div
              key={question.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
            >
              <Card hover className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className={`px-2 py-1 text-xs rounded-full ${getDifficultyColor(question.difficulty)}`}>
                        {question.difficulty}
                      </span>
                      <span className="text-sm text-gray-500">{question.subject} • {question.chapter}</span>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">{question.question_text}</h3>
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="ghost" size="sm" onClick={() => handleEditClick(question)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => handleDeleteClick(question.id)}>
                      <Trash2 className="h-4 w-4 text-red-500" />
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-4">
                  {question.options.map((option, optionIndex) => (
                    <div
                      key={optionIndex}
                      className={`p-3 rounded-lg border ${
                        optionIndex === question.correct_answer
                          ? 'bg-green-50 border-green-200'
                          : 'bg-gray-50 border-gray-200'
                      }`}
                    >
                      <span className="text-sm font-medium text-gray-700">
                        {String.fromCharCode(65 + optionIndex)}. {option}
                      </span>
                      {optionIndex === question.correct_answer && (
                        <span className="text-xs text-green-600 ml-2">✓ Correct</span>
                      )}
                    </div>
                  ))}
                </div>

                {question.explanation && (
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <p className="text-sm text-blue-800">
                      <strong>Explanation:</strong> {question.explanation}
                    </p>
                  </div>
                )}
              </Card>
            </motion.div>
          ))
        )}
      </div>

      {/* Upload Modal */}
      <Modal
        isOpen={showUploadModal}
        onClose={() => setShowUploadModal(false)}
        title="Import Questions from CSV"
      >
        <div className="space-y-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">CSV Format Requirements:</h4>
            <ul className="text-sm text-blue-800 space-y-1 list-disc list-inside">
              <li>`question`: The question text</li>
              <li>`option1`, `option2`, etc.: Answer options</li>
              <li>`correct_answer`: Number (1-based) indicating correct option</li>
              <li>`subject`: Question subject/category</li>
              <li>`chapter`: Chapter or topic</li>
              <li>`difficulty`: easy, medium, or hard</li>
              <li>`explanation`: Optional explanation for the answer</li>
            </ul>
          </div>

          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileUpload}
            accept=".csv"
            className="hidden"
          />

          <Button
            onClick={() => fileInputRef.current?.click()}
            className="w-full"
            disabled={loading}
          >
            <Upload className="h-4 w-4 mr-2" />
            Choose CSV File
          </Button>
        </div>
      </Modal>

      {/* Add/Edit Question Modal */}
      <Modal
        isOpen={showFormModal}
        onClose={() => setShowFormModal(false)}
        title={editingQuestion ? 'Edit Question' : 'Add New Question'}
        size="lg"
      >
        <QuestionForm
          onSubmit={handleFormSubmit}
          onCancel={() => setShowFormModal(false)}
          initialData={editingQuestion}
          loading={loading}
        />
      </Modal>
    </div>
  );
};