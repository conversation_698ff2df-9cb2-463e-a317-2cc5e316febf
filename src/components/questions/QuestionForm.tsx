import React, { useEffect } from 'react';
import { useForm, useFieldArray, SubmitHandler } from 'react-hook-form';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Question, QuestionInsert } from '../../stores/questionStore';
import { Trash2 } from 'lucide-react';

// This is the data structure for the form itself, compatible with useFieldArray
type FormFields = Omit<Question, 'id' | 'created_at' | 'organization_id' | 'options' | 'updated_at'> & {
  options: { value: string }[];
};

// This is the data structure the parent component expects on submit
export type QuestionFormData = Omit<QuestionInsert, 'id' | 'created_at' | 'organization_id' | 'updated_at'>;

interface QuestionFormProps {
  onSubmit: (data: QuestionFormData) => void;
  onCancel: () => void;
  initialData?: Question | null;
  loading: boolean;
}

export const QuestionForm: React.FC<QuestionFormProps> = ({ onSubmit, onCancel, initialData, loading }) => {
  const { register, handleSubmit, control, reset, formState: { errors } } = useForm<FormFields>({
    defaultValues: {
      question_text: '',
      options: [{ value: '' }, { value: '' }, { value: '' }, { value: '' }],
      correct_answer: 0,
      subject: '',
      chapter: '',
      difficulty: 'medium',
      explanation: '',
    }
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "options"
  });

  useEffect(() => {
    if (initialData) {
      reset({
        ...initialData,
        // The form expects { value: string }[] but the data has string[]
        options: initialData.options.map(opt => ({ value: opt })),
      });
    } else {
      reset({
        question_text: '',
        options: [{ value: '' }, { value: '' }, { value: '' }, { value: '' }],
        correct_answer: 0,
        subject: '',
        chapter: '',
        difficulty: 'medium',
        explanation: '',
      });
    }
  }, [initialData, reset]);

  // Transform form data before submitting to parent
  const handleFormSubmit: SubmitHandler<FormFields> = (data) => {
    const transformedData = {
      ...data,
      options: data.options.map(opt => opt.value).filter(Boolean), // Ensure no empty strings
      correct_answer: Number(data.correct_answer),
    };
    onSubmit(transformedData);
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700">Question Text</label>
        <Input
          {...register('question_text', { required: 'Question text is required' })}
          placeholder="What is the capital of France?"
          error={errors.question_text?.message}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Options & Correct Answer</label>
        <p className="text-xs text-gray-500 mb-2">Select the correct answer by clicking the radio button.</p>
        {fields.map((field, index) => (
          <div key={field.id} className="flex items-center space-x-2 mb-2">
            <input
              type="radio"
              {...register('correct_answer')}
              value={index}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
            />
            <Input
              {...register(`options.${index}.value`)}
              placeholder={`Option ${index + 1}`}
              error={errors.options?.[index]?.value?.message}
            />
             <Button type="button" variant="ghost" size="sm" onClick={() => remove(index)}>
                <Trash2 className="h-4 w-4 text-red-500" />
            </Button>
          </div>
        ))}
         <Button type="button" size="sm" onClick={() => append({ value: '' })}>Add Option</Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">Subject</label>
          <Input
            {...register('subject', { required: 'Subject is required' })}
            placeholder="Geography"
            error={errors.subject?.message}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">Chapter</label>
          <Input
            {...register('chapter')}
            placeholder="World Capitals"
            error={errors.chapter?.message}
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Difficulty</label>
        <select
          {...register('difficulty')}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="easy">Easy</option>
          <option value="medium">Medium</option>
          <option value="hard">Hard</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Explanation (Optional)</label>
        <textarea
          {...register('explanation')}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Provide a brief explanation for the correct answer."
        />
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <Button type="button" variant="outline" onClick={onCancel} disabled={loading}>
          Cancel
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? 'Saving...' : (initialData ? 'Update Question' : 'Add Question')}
        </Button>
      </div>
    </form>
  );
};