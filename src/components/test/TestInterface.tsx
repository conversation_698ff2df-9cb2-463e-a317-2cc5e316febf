import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Clock, Flag, CheckCircle2, Circle, AlertTriangle } from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { useTestStore } from '../../stores/testStore';

export const TestInterface: React.FC = () => {
  const { currentAttempt, navigateToQuestion, submitAnswer, markForReview, submitTest, updateTimeSpent } = useTestStore();
  const [timeRemaining, setTimeRemaining] = useState(3600); // 1 hour in seconds
  const [questionStartTime, setQuestionStartTime] = useState(Date.now());

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 1) {
          submitTest();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [submitTest]);

  useEffect(() => {
    setQuestionStartTime(Date.now());
  }, [currentAttempt?.current_question]);

  useEffect(() => {
    return () => {
      if (currentAttempt) {
        const currentQuestion = currentAttempt.questions[currentAttempt.current_question];
        const timeSpent = Math.floor((Date.now() - questionStartTime) / 1000);
        updateTimeSpent(currentQuestion.id, timeSpent);
      }
    };
  }, [currentAttempt?.current_question, questionStartTime, updateTimeSpent]);

  if (!currentAttempt) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">No Active Test</h2>
          <p className="text-gray-600">Please start a test to continue.</p>
        </Card>
      </div>
    );
  }

  const currentQuestion = currentAttempt.questions[currentAttempt.current_question];
  const currentResponse = currentAttempt.responses[currentQuestion.id];

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleAnswerSelect = (optionIndex: number) => {
    submitAnswer(currentQuestion.id, optionIndex);
  };

  const handleMarkForReview = () => {
    markForReview(currentQuestion.id, !currentResponse?.is_marked_for_review);
  };

  const getQuestionStatus = (index: number) => {
    const question = currentAttempt.questions[index];
    const response = currentAttempt.responses[question.id];
    
    if (response?.selected_answer !== undefined) {
      return response.is_marked_for_review ? 'answered-marked' : 'answered';
    } else if (response?.is_marked_for_review) {
      return 'marked';
    }
    return index === currentAttempt.current_question ? 'current' : 'unanswered';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'answered': return 'bg-green-500 text-white';
      case 'answered-marked': return 'bg-orange-500 text-white';
      case 'marked': return 'bg-yellow-500 text-white';
      case 'current': return 'bg-blue-500 text-white';
      default: return 'bg-gray-200 text-gray-700';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-semibold text-gray-900">Test in Progress</h1>
              <p className="text-sm text-gray-600">
                Question {currentAttempt.current_question + 1} of {currentAttempt.total_questions}
              </p>
            </div>
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2 text-lg font-mono">
                <Clock className="h-5 w-5 text-blue-500" />
                <span className={timeRemaining < 300 ? 'text-red-600' : 'text-gray-900'}>
                  {formatTime(timeRemaining)}
                </span>
              </div>
              <Button onClick={submitTest} variant="outline">
                Submit Test
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-6 grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Question Navigation */}
        <div className="lg:col-span-1">
          <Card className="p-4">
            <h3 className="font-semibold mb-4">Questions</h3>
            <div className="grid grid-cols-5 lg:grid-cols-4 gap-2">
              {currentAttempt.questions.map((_, index) => {
                const status = getQuestionStatus(index);
                return (
                  <motion.button
                    key={index}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => navigateToQuestion(index)}
                    className={`w-10 h-10 rounded-lg text-sm font-medium transition-all ${getStatusColor(status)}`}
                  >
                    {index + 1}
                  </motion.button>
                );
              })}
            </div>
            
            {/* Legend */}
            <div className="mt-6 space-y-2 text-xs">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-green-500 rounded"></div>
                <span>Answered</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-orange-500 rounded"></div>
                <span>Answered & Marked</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-yellow-500 rounded"></div>
                <span>Marked for Review</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-blue-500 rounded"></div>
                <span>Current</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-gray-200 rounded"></div>
                <span>Not Visited</span>
              </div>
            </div>
          </Card>
        </div>

        {/* Question Content */}
        <div className="lg:col-span-3">
          <Card className="p-6">
            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                <span className="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">
                  {currentQuestion.subject}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleMarkForReview}
                  className={currentResponse?.is_marked_for_review ? 'bg-yellow-50 border-yellow-300' : ''}
                >
                  <Flag className="h-4 w-4 mr-2" />
                  {currentResponse?.is_marked_for_review ? 'Unmark' : 'Mark for Review'}
                </Button>
              </div>
              
              <h2 className="text-xl font-semibold text-gray-900 leading-relaxed">
                {currentQuestion.question_text}
              </h2>
            </div>

            <div className="space-y-3 mb-8">
              {currentQuestion.options.map((option, index) => (
                <motion.button
                  key={index}
                  whileHover={{ scale: 1.01 }}
                  whileTap={{ scale: 0.99 }}
                  onClick={() => handleAnswerSelect(index)}
                  className={`w-full p-4 text-left rounded-lg border-2 transition-all ${
                    currentResponse?.selected_answer === index
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center">
                    <div className={`w-6 h-6 rounded-full border-2 mr-4 flex items-center justify-center ${
                      currentResponse?.selected_answer === index
                        ? 'border-blue-500 bg-blue-500'
                        : 'border-gray-300'
                    }`}>
                      {currentResponse?.selected_answer === index ? (
                        <CheckCircle2 className="h-4 w-4 text-white" />
                      ) : (
                        <Circle className="h-4 w-4 text-gray-300" />
                      )}
                    </div>
                    <div>
                      <span className="font-medium text-gray-700 mr-3">
                        {String.fromCharCode(65 + index)}.
                      </span>
                      <span className="text-gray-900">{option}</span>
                    </div>
                  </div>
                </motion.button>
              ))}
            </div>

            {/* Navigation */}
            <div className="flex items-center justify-between">
              <Button
                variant="outline"
                onClick={() => navigateToQuestion(currentAttempt.current_question - 1)}
                disabled={currentAttempt.current_question === 0}
              >
                Previous
              </Button>

              <div className="flex space-x-3">
                {currentResponse?.selected_answer === undefined && (
                  <div className="flex items-center text-amber-600 text-sm">
                    <AlertTriangle className="h-4 w-4 mr-1" />
                    Not answered
                  </div>
                )}
              </div>

              <Button
                onClick={() => navigateToQuestion(currentAttempt.current_question + 1)}
                disabled={currentAttempt.current_question === currentAttempt.total_questions - 1}
              >
                Next
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};