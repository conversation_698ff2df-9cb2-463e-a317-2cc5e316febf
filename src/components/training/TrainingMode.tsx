import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, Play, Pause, RotateCcw, Pencil } from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { DrawingCanvas } from './DrawingCanvas';

interface Question {
  id: string;
  question_text: string;
  options: string[];
  correct_answer: number;
  subject: string;
  explanation?: string;
}

const mockQuestions: Question[] = [
  {
    id: '1',
    question_text: 'What is the derivative of f(x) = x²?',
    options: ['2x', 'x', '2', 'x²'],
    correct_answer: 0,
    subject: 'Mathematics',
    explanation: 'Using the power rule: d/dx(x²) = 2x¹ = 2x'
  },
  {
    id: '2',
    question_text: 'Which of the following is a renewable energy source?',
    options: ['Coal', 'Natural Gas', 'Solar Power', 'Nuclear Power'],
    correct_answer: 2,
    subject: 'Science',
    explanation: 'Solar power is renewable because it harnesses energy from the sun, which is continuously available.'
  }
];

export const TrainingMode: React.FC = () => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [showAnswers, setShowAnswers] = useState(false);
  const [revealedOptions, setRevealedOptions] = useState<number[]>([]);
  const [showDrawing, setShowDrawing] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);

  const currentQuestion = mockQuestions[currentQuestionIndex];

  const handleNextQuestion = () => {
    if (currentQuestionIndex < mockQuestions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      resetQuestion();
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
      resetQuestion();
    }
  };

  const resetQuestion = () => {
    setShowAnswers(false);
    setRevealedOptions([]);
  };

  const revealNextOption = () => {
    if (revealedOptions.length < currentQuestion.options.length) {
      setRevealedOptions([...revealedOptions, revealedOptions.length]);
    } else {
      setShowAnswers(true);
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'ArrowRight' || event.key === ' ') {
      event.preventDefault();
      revealNextOption();
    } else if (event.key === 'ArrowLeft') {
      event.preventDefault();
      if (revealedOptions.length > 0) {
        setRevealedOptions(revealedOptions.slice(0, -1));
      }
    }
  };

  return (
    <div 
      className="space-y-6 outline-none" 
      onKeyDown={handleKeyPress}
      tabIndex={0}
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Training Mode</h1>
          <p className="text-gray-600 mt-2">
            Interactive teaching with animated reveals and drawing capabilities
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            onClick={() => setShowDrawing(!showDrawing)}
          >
            <Pencil className="h-4 w-4 mr-2" />
            {showDrawing ? 'Hide' : 'Show'} Drawing
          </Button>
          <div className="bg-white px-3 py-1 rounded-lg border">
            <span className="text-sm text-gray-600">
              {currentQuestionIndex + 1} of {mockQuestions.length}
            </span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Question Area */}
        <div className="lg:col-span-2 space-y-6">
          {/* Question Card */}
          <Card className="p-8">
            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                <span className="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">
                  {currentQuestion.subject}
                </span>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={resetQuestion}
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <h2 className="text-2xl font-semibold text-gray-900 leading-relaxed">
                {currentQuestion.question_text}
              </h2>
            </div>

            {/* Options with animated reveal */}
            <div className="space-y-4 mb-6">
              <AnimatePresence>
                {currentQuestion.options.map((option, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -50 }}
                    animate={{ 
                      opacity: revealedOptions.includes(index) ? 1 : 0.3,
                      x: revealedOptions.includes(index) ? 0 : -20
                    }}
                    transition={{ 
                      duration: 0.5,
                      ease: "easeOut"
                    }}
                    className={`p-4 rounded-lg border-2 transition-all duration-300 ${
                      showAnswers && index === currentQuestion.correct_answer
                        ? 'border-green-500 bg-green-50'
                        : showAnswers && revealedOptions.includes(index)
                        ? 'border-red-200 bg-red-50'
                        : 'border-gray-200 bg-white'
                    }`}
                  >
                    <div className="flex items-center">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium mr-3 ${
                        showAnswers && index === currentQuestion.correct_answer
                          ? 'bg-green-500 text-white'
                          : 'bg-gray-100 text-gray-600'
                      }`}>
                        {String.fromCharCode(65 + index)}
                      </div>
                      <span className="text-lg">{option}</span>
                      {showAnswers && index === currentQuestion.correct_answer && (
                        <motion.span
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          className="ml-auto text-green-500 font-bold"
                        >
                          ✓ Correct
                        </motion.span>
                      )}
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>

            {/* Explanation */}
            <AnimatePresence>
              {showAnswers && currentQuestion.explanation && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="bg-blue-50 p-4 rounded-lg border border-blue-200"
                >
                  <h4 className="font-semibold text-blue-900 mb-2">Explanation:</h4>
                  <p className="text-blue-800">{currentQuestion.explanation}</p>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Controls */}
            <div className="flex items-center justify-between mt-8">
              <Button
                variant="outline"
                onClick={handlePreviousQuestion}
                disabled={currentQuestionIndex === 0}
              >
                <ChevronLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>

              <div className="flex space-x-3">
                <Button
                  onClick={revealNextOption}
                  disabled={showAnswers}
                >
                  {revealedOptions.length < currentQuestion.options.length ? (
                    <>
                      <Play className="h-4 w-4 mr-2" />
                      Reveal Next
                    </>
                  ) : (
                    'Show Answer'
                  )}
                </Button>
              </div>

              <Button
                variant="outline"
                onClick={handleNextQuestion}
                disabled={currentQuestionIndex === mockQuestions.length - 1}
              >
                Next
                <ChevronRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </Card>

          {/* Instructions */}
          <Card className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50">
            <h3 className="font-medium mb-2">Training Controls</h3>
            <div className="text-sm text-gray-600 space-y-1">
              <p>• Press <kbd className="px-2 py-1 bg-white rounded text-xs">Space</kbd> or <kbd className="px-2 py-1 bg-white rounded text-xs">→</kbd> to reveal next option</p>
              <p>• Press <kbd className="px-2 py-1 bg-white rounded text-xs">←</kbd> to go back one step</p>
              <p>• Use drawing panel to explain concepts visually</p>
            </div>
          </Card>
        </div>

        {/* Drawing Panel */}
        <div className="lg:col-span-1">
          <AnimatePresence>
            {showDrawing && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
              >
                <DrawingCanvas />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
};