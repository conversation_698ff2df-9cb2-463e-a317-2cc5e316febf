import React from 'react';
import { motion } from 'framer-motion';
import { Zap } from 'lucide-react';

interface BoltBadgeProps {
  variant?: 'footer' | 'floating' | 'inline';
  className?: string;
}

export const BoltBadge: React.FC<BoltBadgeProps> = ({ 
  variant = 'footer',
  className = '' 
}) => {
  const baseClasses = "inline-flex items-center space-x-2 text-sm font-medium transition-all duration-200";
  
  const variants = {
    footer: "text-gray-600 hover:text-blue-600 bg-white/80 backdrop-blur-sm px-3 py-2 rounded-lg border border-gray-200 hover:border-blue-300 shadow-sm hover:shadow-md",
    floating: "fixed bottom-4 right-4 z-50 bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-4 py-2 rounded-full shadow-lg hover:shadow-xl hover:scale-105",
    inline: "text-gray-500 hover:text-blue-600 bg-gray-50 hover:bg-blue-50 px-3 py-1 rounded-md border border-gray-200 hover:border-blue-300"
  };

  const Component = variant === 'floating' ? motion.a : 'a';
  const motionProps = variant === 'floating' ? {
    whileHover: { scale: 1.05 },
    whileTap: { scale: 0.95 },
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { delay: 1 }
  } : {};

  return (
    <Component
      href="https://bolt.new"
      target="_blank"
      rel="noopener noreferrer"
      className={`${baseClasses} ${variants[variant]} ${className}`}
      {...motionProps}
    >
      <Zap className="h-4 w-4" />
      <span>Built with Bolt</span>
    </Component>
  );
};