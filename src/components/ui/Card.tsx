import React from 'react';
import { motion } from 'framer-motion';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
}

export const Card: React.FC<CardProps> = ({ children, className = '', hover = false }) => {
  const Component = hover ? motion.div : 'div';
  const motionProps = hover ? {
    whileHover: { 
      y: -4, 
      boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.15), 0 10px 20px -5px rgba(0, 0, 0, 0.1)' 
    },
    transition: { duration: 0.3, ease: "easeOut" }
  } : {};

  return (
    <Component
      className={`bg-white rounded-xl shadow-lg border border-gray-100 backdrop-blur-sm ${className}`}
      {...motionProps}
    >
      {children}
    </Component>
  );
};