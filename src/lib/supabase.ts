import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

export type Database = {
  public: {
    Tables: {
      organizations: {
        Row: {
          id: string;
          name: string;
          plan: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          plan?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          plan?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      profiles: {
        Row: {
          id: string;
          email: string;
          full_name: string;
          role: 'admin' | 'trainer' | 'candidate';
          organization_id: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          full_name: string;
          role?: 'admin' | 'trainer' | 'candidate';
          organization_id: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          full_name?: string;
          role?: 'admin' | 'trainer' | 'candidate';
          organization_id?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      questions: {
        Row: {
          id: string;
          organization_id: string;
          question_text: string;
          options: string[];
          correct_answer: number;
          subject: string;
          chapter: string;
          difficulty: 'easy' | 'medium' | 'hard';
          explanation?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          organization_id: string;
          question_text: string;
          options: string[];
          correct_answer: number;
          subject: string;
          chapter: string;
          difficulty?: 'easy' | 'medium' | 'hard';
          explanation?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          organization_id?: string;
          question_text?: string;
          options?: string[];
          correct_answer?: number;
          subject?: string;
          chapter?: string;
          difficulty?: 'easy' | 'medium' | 'hard';
          explanation?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      tests: {
        Row: {
          id: string;
          organization_id: string;
          title: string;
          description?: string;
          duration_minutes: number;
          created_by: string;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          organization_id: string;
          title: string;
          description?: string;
          duration_minutes: number;
          created_by: string;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          organization_id?: string;
          title?: string;
          description?: string;
          duration_minutes?: number;
          created_by?: string;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      test_questions: {
        Row: {
          id: string;
          test_id: string;
          question_id: string;
          order_index: number;
          created_at: string;
        };
        Insert: {
          id?: string;
          test_id: string;
          question_id: string;
          order_index: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          test_id?: string;
          question_id?: string;
          order_index?: number;
          created_at?: string;
        };
      };
      test_attempts: {
        Row: {
          id: string;
          test_id: string;
          candidate_id: string;
          started_at: string;
          completed_at?: string;
          score?: number;
          total_questions: number;
          correct_answers: number;
          time_spent_seconds: number;
          created_at: string;
        };
        Insert: {
          id?: string;
          test_id: string;
          candidate_id: string;
          started_at?: string;
          completed_at?: string;
          score?: number;
          total_questions: number;
          correct_answers?: number;
          time_spent_seconds?: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          test_id?: string;
          candidate_id?: string;
          started_at?: string;
          completed_at?: string;
          score?: number;
          total_questions?: number;
          correct_answers?: number;
          time_spent_seconds?: number;
          created_at?: string;
        };
      };
      question_responses: {
        Row: {
          id: string;
          attempt_id: string;
          question_id: string;
          selected_answer?: number;
          is_marked_for_review: boolean;
          time_spent_seconds: number;
          answered_at?: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          attempt_id: string;
          question_id: string;
          selected_answer?: number;
          is_marked_for_review?: boolean;
          time_spent_seconds?: number;
          answered_at?: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          attempt_id?: string;
          question_id?: string;
          selected_answer?: number;
          is_marked_for_review?: boolean;
          time_spent_seconds?: number;
          answered_at?: string;
          created_at?: string;
        };
      };
    };
  };
};