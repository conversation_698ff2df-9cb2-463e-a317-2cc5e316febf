import { create } from 'zustand';
import { User } from '@supabase/supabase-js';
import { supabase } from '../lib/supabase';

interface Profile {
  id: string;
  email: string;
  full_name: string;
  role: 'admin' | 'trainer' | 'candidate';
  organization_id: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface AuthState {
  user: User | null;
  profile: Profile | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, fullName: string, role: string, organizationName?: string) => Promise<void>;
  signOut: () => Promise<void>;
  fetchProfile: () => Promise<void>;
  setUser: (user: User | null) => void;
  signInWithGoogle: () => Promise<void>;
  demoSignIn: (role: 'trainer' | 'candidate') => Promise<void>;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  profile: null,
  loading: true,

  signIn: async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw error;
    
    set({ user: data.user });
    await get().fetchProfile();
  },

  signUp: async (email: string, password: string, fullName: string, role: string, organizationName?: string) => {
    try {
      // First, sign up the user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
      });

      if (authError) {
        throw new Error(`Sign up failed: ${authError.message}`);
      }
      
      if (!authData.user) {
        throw new Error('User signup did not return a user.');
      }

      // Now handle organization and profile creation
      let organizationId: string;

      if (role === 'trainer') {
        // For trainers, create a new organization
        const orgName = organizationName || `${fullName}'s Training Organization`;
        
        const { data: orgData, error: orgError } = await supabase
          .from('organizations')
          .insert({ name: orgName })
          .select('id')
          .single();

        if (orgError) {
          throw new Error(`Failed to create organization: ${orgError.message}`);
        }
        if (!orgData) {
          throw new Error('Failed to create organization.');
        }
        organizationId = orgData.id;
      } else {
        // For candidates, use the demo organization
        organizationId = '550e8400-e29b-41d4-a716-446655440001';
      }

      // Create the user's profile
      const { error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: authData.user.id,
          email,
          full_name: fullName,
          role: role as 'admin' | 'trainer' | 'candidate',
          organization_id: organizationId,
          is_active: true,
        });

      if (profileError) {
        throw new Error(`Failed to create profile: ${profileError.message}`);
      }

      // Success! Don't set user here as they may need to verify email
      
    } catch (error) {
      console.error('Signup error:', error);
      throw error;
    }
  },

  signInWithGoogle: async () => {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}`,
      },
    });
    if (error) throw error;
  },

  demoSignIn: async (role: 'trainer' | 'candidate') => {
    const demoCredentials = {
      trainer: { email: '<EMAIL>', password: 'demo123456' },
      candidate: { email: '<EMAIL>', password: 'demo123456' }
    };

    const credentials = demoCredentials[role];
    
    try {
      // Try to sign in first
      await get().signIn(credentials.email, credentials.password);
    } catch (error) {
      // If sign in fails, create the demo user
      try {
        await get().signUp(
          credentials.email,
          credentials.password,
          `Demo ${role.charAt(0).toUpperCase() + role.slice(1)}`,
          role
        );

        // After creating, try to sign in
        await get().signIn(credentials.email, credentials.password);
        
      } catch (signUpError) {
        // If signup also fails, try signing in again (user might exist)
        try {
          await get().signIn(credentials.email, credentials.password);
        } catch (finalError) {
          throw new Error(`Demo login failed: ${finalError instanceof Error ? finalError.message : 'Unknown error'}`);
        }
      }
    }
  },

  signOut: async () => {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
    set({ user: null, profile: null });
  },

  fetchProfile: async () => {
    const user = get().user;
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('Error fetching profile:', error);
        throw error;
      }
      
      set({ profile: data });
    } catch (error) {
      console.error('Failed to fetch profile:', error);
      // Don't throw here, just log the error
    }
  },

  setUser: (user: User | null) => {
    set({ user, loading: false });
    if (user) {
      get().fetchProfile();
    } else {
      set({ profile: null });
    }
  },
}));