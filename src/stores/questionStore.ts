import { create } from 'zustand';
import { supabase } from '../lib/supabase';
import { Database } from '../lib/supabase';

export type Question = Database['public']['Tables']['questions']['Row'];
export type QuestionInsert = Database['public']['Tables']['questions']['Insert'];
export type QuestionUpdate = Database['public']['Tables']['questions']['Update'];

interface QuestionState {
  questions: Question[];
  loading: boolean;
  fetchQuestions: () => Promise<void>;
  addQuestion: (question: QuestionInsert) => Promise<void>;
  updateQuestion: (id: string, question: QuestionUpdate) => Promise<void>;
  deleteQuestion: (id: string) => Promise<void>;
  uploadQuestions: (questions: QuestionInsert[]) => Promise<void>;
}

export const useQuestionStore = create<QuestionState>((set, get) => ({
  questions: [],
  loading: false,

  fetchQuestions: async () => {
    set({ loading: true });
    try {
      const { data, error } = await supabase
        .from('questions')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      set({ questions: data || [], loading: false });
    } catch (error) {
      console.error('Error fetching questions:', error);
      set({ loading: false });
    }
  },

  addQuestion: async (question) => {
    const { data, error } = await supabase
      .from('questions')
      .insert(question)
      .select()
      .single();

    if (error) throw error;
    if (data) {
      set((state) => ({ questions: [data, ...state.questions] }));
    }
  },

  updateQuestion: async (id, question) => {
    const { data, error } = await supabase
      .from('questions')
      .update(question)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    if (data) {
      set((state) => ({
        questions: state.questions.map((q) => (q.id === id ? data : q)),
      }));
    }
  },

  deleteQuestion: async (id) => {
    const { error } = await supabase.from('questions').delete().eq('id', id);

    if (error) throw error;
    set((state) => ({
      questions: state.questions.filter((q) => q.id !== id),
    }));
  },

  uploadQuestions: async (questions) => {
    set({ loading: true });
    try {
      const { error } = await supabase.from('questions').insert(questions);
      if (error) throw error;
      // Refresh the questions list after upload
      await get().fetchQuestions();
    } catch (error) {
      console.error('Error uploading questions:', error);
      // The fetchQuestions call will set loading to false
    }
  },
}));