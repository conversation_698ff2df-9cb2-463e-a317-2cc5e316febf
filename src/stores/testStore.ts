import { create } from 'zustand';
import { supabase } from '../lib/supabase';

interface Question {
  id: string;
  question_text: string;
  options: string[];
  correct_answer: number;
  subject: string;
  chapter: string;
  difficulty: 'easy' | 'medium' | 'hard';
  explanation?: string;
}

interface TestAttempt {
  id: string;
  test_id: string;
  started_at: string;
  total_questions: number;
  current_question: number;
  time_spent_seconds: number;
  questions: Question[];
  responses: Record<string, {
    selected_answer?: number;
    is_marked_for_review: boolean;
    time_spent_seconds: number;
  }>;
}

interface TestState {
  currentAttempt: TestAttempt | null;
  loading: boolean;
  startTest: (testId: string) => Promise<void>;
  submitAnswer: (questionId: string, answer: number) => Promise<void>;
  markForReview: (questionId: string, marked: boolean) => Promise<void>;
  navigateToQuestion: (index: number) => void;
  submitTest: () => Promise<void>;
  updateTimeSpent: (questionId: string, timeSpent: number) => void;
}

export const useTestStore = create<TestState>((set, get) => ({
  currentAttempt: null,
  loading: false,

  startTest: async (testId: string) => {
    set({ loading: true });
    try {
      // Get test questions
      const { data: testQuestions, error: questionsError } = await supabase
        .from('test_questions')
        .select(`
          question_id,
          order_index,
          questions (*)
        `)
        .eq('test_id', testId)
        .order('order_index');

      if (questionsError) throw questionsError;

      // Create test attempt
      const { data: attempt, error: attemptError } = await supabase
        .from('test_attempts')
        .insert({
          test_id: testId,
          candidate_id: (await supabase.auth.getUser()).data.user?.id!,
          total_questions: testQuestions.length,
          started_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (attemptError) throw attemptError;

      const questions = testQuestions.map(tq => tq.questions).filter(Boolean);
      
      set({
        currentAttempt: {
          id: attempt.id,
          test_id: testId,
          started_at: attempt.started_at,
          total_questions: questions.length,
          current_question: 0,
          time_spent_seconds: 0,
          questions,
          responses: {},
        },
        loading: false,
      });
    } catch (error) {
      console.error('Error starting test:', error);
      set({ loading: false });
    }
  },

  submitAnswer: async (questionId: string, answer: number) => {
    const attempt = get().currentAttempt;
    if (!attempt) return;

    const response = attempt.responses[questionId] || {
      is_marked_for_review: false,
      time_spent_seconds: 0,
    };

    const updatedResponse = {
      ...response,
      selected_answer: answer,
    };

    set({
      currentAttempt: {
        ...attempt,
        responses: {
          ...attempt.responses,
          [questionId]: updatedResponse,
        },
      },
    });

    // Save to database
    await supabase
      .from('question_responses')
      .upsert({
        attempt_id: attempt.id,
        question_id: questionId,
        selected_answer: answer,
        is_marked_for_review: updatedResponse.is_marked_for_review,
        time_spent_seconds: updatedResponse.time_spent_seconds,
        answered_at: new Date().toISOString(),
      });
  },

  markForReview: async (questionId: string, marked: boolean) => {
    const attempt = get().currentAttempt;
    if (!attempt) return;

    const response = attempt.responses[questionId] || {
      time_spent_seconds: 0,
    };

    const updatedResponse = {
      ...response,
      is_marked_for_review: marked,
    };

    set({
      currentAttempt: {
        ...attempt,
        responses: {
          ...attempt.responses,
          [questionId]: updatedResponse,
        },
      },
    });

    // Save to database
    await supabase
      .from('question_responses')
      .upsert({
        attempt_id: attempt.id,
        question_id: questionId,
        selected_answer: response.selected_answer,
        is_marked_for_review: marked,
        time_spent_seconds: response.time_spent_seconds,
      });
  },

  navigateToQuestion: (index: number) => {
    const attempt = get().currentAttempt;
    if (!attempt) return;

    set({
      currentAttempt: {
        ...attempt,
        current_question: index,
      },
    });
  },

  updateTimeSpent: (questionId: string, timeSpent: number) => {
    const attempt = get().currentAttempt;
    if (!attempt) return;

    const response = attempt.responses[questionId] || {
      is_marked_for_review: false,
    };

    set({
      currentAttempt: {
        ...attempt,
        responses: {
          ...attempt.responses,
          [questionId]: {
            ...response,
            time_spent_seconds: timeSpent,
          },
        },
      },
    });
  },

  submitTest: async () => {
    const attempt = get().currentAttempt;
    if (!attempt) return;

    const correctAnswers = Object.entries(attempt.responses).reduce((count, [questionId, response]) => {
      const question = attempt.questions.find(q => q.id === questionId);
      if (question && response.selected_answer === question.correct_answer) {
        return count + 1;
      }
      return count;
    }, 0);

    const totalTimeSpent = Object.values(attempt.responses).reduce((total, response) => {
      return total + response.time_spent_seconds;
    }, 0);

    const score = Math.round((correctAnswers / attempt.total_questions) * 100);

    await supabase
      .from('test_attempts')
      .update({
        completed_at: new Date().toISOString(),
        score,
        correct_answers: correctAnswers,
        time_spent_seconds: totalTimeSpent,
      })
      .eq('id', attempt.id);

    set({ currentAttempt: null });
  },
}));