/*
  # Fix Authentication and Database Setup

  1. Database Structure
    - Fix users table creation
    - Update profiles table with missing columns
    - Create demo organizations with proper UUIDs
    
  2. Security
    - Enable RLS on all tables
    - Create proper policies for different roles
    - Fix trigger functions
    
  3. Demo Data
    - Create demo organizations
    - Set up proper UUID handling
*/

-- Create users table that works with <PERSON><PERSON><PERSON> auth
CREATE TABLE IF NOT EXISTS users (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  email text UNIQUE NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Update profiles table structure
DO $$
BEGIN
  -- Add missing columns if they don't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'profiles' AND column_name = 'is_active'
  ) THEN
    ALTER TABLE profiles ADD COLUMN is_active boolean DEFAULT true;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'profiles' AND column_name = 'last_login'
  ) THEN
    ALTER TABLE profiles ADD COLUMN last_login timestamptz;
  END IF;
END $$;

-- Enable RLS on users table
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create policies for users table
DO $$
BEGIN
  -- Drop existing policies if they exist
  DROP POLICY IF EXISTS "Users can view their own data" ON users;
  DROP POLICY IF EXISTS "Users can update their own data" ON users;
  
  -- Create new policies
  CREATE POLICY "Users can view their own data"
    ON users
    FOR SELECT
    TO authenticated
    USING (auth.uid() = id);

  CREATE POLICY "Users can update their own data"
    ON users
    FOR UPDATE
    TO authenticated
    USING (auth.uid() = id);
END $$;

-- Update profiles policies to be more permissive for trainers
DO $$
BEGIN
  -- Drop existing policies if they exist
  DROP POLICY IF EXISTS "Admins can view any profile in their org" ON profiles;
  DROP POLICY IF EXISTS "Admins can view profiles in their organization" ON profiles;
  DROP POLICY IF EXISTS "Trainers and admins can view profiles in their organization" ON profiles;
  DROP POLICY IF EXISTS "Trainers and admins can manage profiles in their organization" ON profiles;

  -- Create new policies for trainers and admins
  CREATE POLICY "Trainers and admins can view profiles in their organization"
    ON profiles
    FOR SELECT
    TO authenticated
    USING (
      organization_id = (
        SELECT organization_id FROM profiles WHERE id = auth.uid()
      ) AND (
        SELECT role FROM profiles WHERE id = auth.uid()
      ) IN ('admin', 'trainer')
    );

  CREATE POLICY "Trainers and admins can manage profiles in their organization"
    ON profiles
    FOR ALL
    TO authenticated
    USING (
      organization_id = (
        SELECT organization_id FROM profiles WHERE id = auth.uid()
      ) AND (
        SELECT role FROM profiles WHERE id = auth.uid()
      ) IN ('admin', 'trainer')
    );
END $$;

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO users (id, email)
  VALUES (new.id, new.email)
  ON CONFLICT (id) DO NOTHING;
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user registration
DO $$
BEGIN
  DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
  CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();
END $$;

-- Create demo organizations with proper UUIDs
DO $$
BEGIN
  -- Insert demo organizations if they don't exist
  INSERT INTO organizations (id, name) 
  VALUES 
    ('550e8400-e29b-41d4-a716-************'::uuid, 'TestMatic Demo Organization'),
    ('550e8400-e29b-41d4-a716-************'::uuid, 'Training Institute Demo')
  ON CONFLICT (id) DO NOTHING;
END $$;