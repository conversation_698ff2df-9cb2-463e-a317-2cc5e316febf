-- Create Organizations Table
CREATE TABLE organizations (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create Profiles Table
-- This table will be populated by a trigger when a new user signs up.
CREATE TABLE profiles (
    id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE,
    full_name TEXT,
    email TEXT,
    role TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create Questions Table
CREATE TABLE questions (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id uuid NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    question_text TEXT NOT NULL,
    options JSONB NOT NULL,
    correct_answer INTEGER NOT NULL,
    subject TEXT,
    chapter TEXT,
    difficulty TEXT,
    explanation TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create Tests Table
CREATE TABLE tests (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id uuid NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create Test_Questions Join Table
CREATE TABLE test_questions (
    test_id uuid NOT NULL REFERENCES tests(id) ON DELETE CASCADE,
    question_id uuid NOT NULL REFERENCES questions(id) ON DELETE CASCADE,
    PRIMARY KEY (test_id, question_id)
);

-- Create Test_Results Table
CREATE TABLE test_results (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    test_id uuid NOT NULL REFERENCES tests(id) ON DELETE CASCADE,
    user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    score INTEGER,
    answers JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable Row Level Security for all tables
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE tests ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_results ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies
-- Organizations
CREATE POLICY "Users can view their own organization" ON organizations
    FOR SELECT USING (id = (SELECT organization_id FROM profiles WHERE id = auth.uid()));

-- Profiles
CREATE POLICY "Users can view their own profile" ON profiles
    FOR SELECT USING (id = auth.uid());
CREATE POLICY "Admins can view profiles in their organization" ON profiles
    FOR SELECT USING (organization_id = (SELECT organization_id FROM profiles WHERE id = auth.uid()) AND (SELECT role FROM profiles WHERE id = auth.uid()) = 'admin');
CREATE POLICY "Users can update their own profile" ON profiles
    FOR UPDATE USING (id = auth.uid());

-- Questions
CREATE POLICY "Admins and trainers can manage questions in their organization" ON questions
    FOR ALL USING (organization_id = (SELECT organization_id FROM profiles WHERE id = auth.uid()) AND ((SELECT role FROM profiles WHERE id = auth.uid()) IN ('admin', 'trainer')));

-- Tests
CREATE POLICY "Admins and trainers can manage tests in their organization" ON tests
    FOR ALL USING (organization_id = (SELECT organization_id FROM profiles WHERE id = auth.uid()) AND ((SELECT role FROM profiles WHERE id = auth.uid()) IN ('admin', 'trainer')));
CREATE POLICY "Candidates can view tests in their organization" ON tests
    FOR SELECT USING (organization_id = (SELECT organization_id FROM profiles WHERE id = auth.uid()));

-- Test Questions
CREATE POLICY "Users can view test questions in their organization" ON test_questions
    FOR SELECT USING (
        (SELECT organization_id FROM tests WHERE id = test_id) = (SELECT organization_id FROM profiles WHERE id = auth.uid())
    );

-- Test Results
CREATE POLICY "Users can view their own test results" ON test_results
    FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "Admins and trainers can view test results in their organization" ON test_results
    FOR SELECT USING (
        (SELECT organization_id FROM tests WHERE id = test_id) = (SELECT organization_id FROM profiles WHERE id = auth.uid())
        AND ((SELECT role FROM profiles WHERE id = auth.uid()) IN ('admin', 'trainer'))
    );
CREATE POLICY "Candidates can create their own test results" ON test_results
    FOR INSERT WITH CHECK (user_id = auth.uid());