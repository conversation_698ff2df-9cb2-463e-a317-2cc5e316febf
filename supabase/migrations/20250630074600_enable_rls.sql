-- Phase 1: Backend Foundation & Security
-- Step 2: Enable Row Level Security (RLS) and define policies.
-- This script is idempotent and can be run multiple times.

-- Helper function to get the organization_id of the currently authenticated user.
CREATE OR REPLACE FUNCTION public.get_my_organization_id()
RETURNS uuid
LANGUAGE sql STABLE
AS $$
  SELECT organization_id FROM public.profiles WHERE id = auth.uid();
$$;

-- Helper function to get the role of the currently authenticated user.
CREATE OR REPLACE FUNCTION public.get_my_role()
RETURNS text
LANGUAGE sql STABLE
AS $$
  SELECT role FROM public.profiles WHERE id = auth.uid();
$$;

-- ---------------------------------------------------------------------------
-- Table: organizations
-- ---------------------------------------------------------------------------
ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Users can view their own organization" ON public.organizations;
CREATE POLICY "Users can view their own organization"
ON public.organizations FOR SELECT
USING (id = public.get_my_organization_id());

-- ---------------------------------------------------------------------------
-- Table: profiles
-- ---------------------------------------------------------------------------
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;
CREATE POLICY "Users can view their own profile"
ON public.profiles FOR SELECT
USING (id = auth.uid());

DROP POLICY IF EXISTS "Admins can view any profile in their org" ON public.profiles;
CREATE POLICY "Admins can view any profile in their org"
ON public.profiles FOR SELECT
USING (organization_id = public.get_my_organization_id() AND public.get_my_role() = 'admin');

DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;
CREATE POLICY "Users can update their own profile"
ON public.profiles FOR UPDATE
USING (id = auth.uid())
WITH CHECK (id = auth.uid());

-- ---------------------------------------------------------------------------
-- Table: questions
-- ---------------------------------------------------------------------------
ALTER TABLE public.questions ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Admins/Trainers can manage questions" ON public.questions;
CREATE POLICY "Admins/Trainers can manage questions"
ON public.questions FOR ALL
USING (organization_id = public.get_my_organization_id() AND public.get_my_role() IN ('admin', 'trainer'));

DROP POLICY IF EXISTS "Users can view questions in their org" ON public.questions;
CREATE POLICY "Users can view questions in their org"
ON public.questions FOR SELECT
USING (organization_id = public.get_my_organization_id());

-- ---------------------------------------------------------------------------
-- Table: tests
-- ---------------------------------------------------------------------------
ALTER TABLE public.tests ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Admins/Trainers can manage tests" ON public.tests;
CREATE POLICY "Admins/Trainers can manage tests"
ON public.tests FOR ALL
USING (organization_id = public.get_my_organization_id() AND public.get_my_role() IN ('admin', 'trainer'));

DROP POLICY IF EXISTS "Users can view tests in their org" ON public.tests;
CREATE POLICY "Users can view tests in their org"
ON public.tests FOR SELECT
USING (organization_id = public.get_my_organization_id());

-- ---------------------------------------------------------------------------
-- Table: test_questions
-- ---------------------------------------------------------------------------
ALTER TABLE public.test_questions ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Users can view test_questions if they can view the test" ON public.test_questions;
CREATE POLICY "Users can view test_questions if they can view the test"
ON public.test_questions FOR SELECT
USING (EXISTS (SELECT 1 FROM public.tests WHERE tests.id = test_questions.test_id));

DROP POLICY IF EXISTS "Admins/Trainers can manage test_questions" ON public.test_questions;
CREATE POLICY "Admins/Trainers can manage test_questions"
ON public.test_questions FOR ALL
USING (EXISTS (SELECT 1 FROM public.tests WHERE tests.id = test_questions.test_id AND public.get_my_role() IN ('admin', 'trainer')));

-- ---------------------------------------------------------------------------
-- Table: test_results
-- ---------------------------------------------------------------------------
ALTER TABLE public.test_results ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Candidates can view their own results" ON public.test_results;
CREATE POLICY "Candidates can view their own results"
ON public.test_results FOR SELECT
USING (user_id = auth.uid());

DROP POLICY IF EXISTS "Admins/Trainers can view results in their org" ON public.test_results;
CREATE POLICY "Admins/Trainers can view results in their org"
ON public.test_results FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE profiles.id = test_results.user_id
    AND profiles.organization_id = public.get_my_organization_id()
  ) AND public.get_my_role() IN ('admin', 'trainer')
);

DROP POLICY IF EXISTS "Candidates can create their own results" ON public.test_results;
CREATE POLICY "Candidates can create their own results"
ON public.test_results FOR INSERT
WITH CHECK (user_id = auth.uid());