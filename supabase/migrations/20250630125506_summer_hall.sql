/*
  # Fix Organization RLS Policies

  1. Security Updates
    - Allow authenticated users to create organizations
    - Allow users to read organizations they belong to
    - Fix signup process permissions

  2. Changes
    - Update organization RLS policies
    - Add proper permissions for organization creation
    - Ensure demo organizations are accessible
*/

-- Update organization RLS policies to allow creation during signup
DO $$
BEGIN
  -- Drop existing policies if they exist
  DROP POLICY IF EXISTS "Users can view their own organization" ON organizations;
  DROP POLICY IF EXISTS "Users can create organizations" ON organizations;
  DROP POLICY IF EXISTS "Authenticated users can create organizations" ON organizations;
  
  -- Allow authenticated users to create organizations (for trainers during signup)
  CREATE POLICY "Authenticated users can create organizations"
    ON organizations
    FOR INSERT
    TO authenticated
    WITH CHECK (true);
    
  -- Allow users to view organizations they belong to
  CREATE POLICY "Users can view their organization"
    ON organizations
    FOR SELECT
    TO authenticated
    USING (
      id IN (
        SELECT organization_id 
        FROM profiles 
        WHERE profiles.id = auth.uid()
      )
      OR id = '550e8400-e29b-41d4-a716-************'::uuid -- Demo org
    );
    
  -- Allow users to update their own organization (for admins/trainers)
  CREATE POLICY "Admins and trainers can update their organization"
    ON organizations
    FOR UPDATE
    TO authenticated
    USING (
      id IN (
        SELECT organization_id 
        FROM profiles 
        WHERE profiles.id = auth.uid() 
        AND role IN ('admin', 'trainer')
      )
    );
END $$;

-- Update profiles policies to allow creation during signup
DO $$
BEGIN
  -- Drop existing policies if they exist
  DROP POLICY IF EXISTS "Users can create their own profile" ON profiles;
  DROP POLICY IF EXISTS "Authenticated users can create profiles" ON profiles;
  
  -- Allow authenticated users to create their own profile during signup
  CREATE POLICY "Users can create their own profile"
    ON profiles
    FOR INSERT
    TO authenticated
    WITH CHECK (id = auth.uid());
END $$;

-- Create helper functions for better RLS
CREATE OR REPLACE FUNCTION get_my_organization_id()
RETURNS uuid AS $$
BEGIN
  RETURN (
    SELECT organization_id 
    FROM profiles 
    WHERE id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION get_my_role()
RETURNS text AS $$
BEGIN
  RETURN (
    SELECT role 
    FROM profiles 
    WHERE id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update existing policies to use helper functions
DO $$
BEGIN
  -- Update questions policies
  DROP POLICY IF EXISTS "Admins/Trainers can manage questions" ON questions;
  DROP POLICY IF EXISTS "Users can view questions in their org" ON questions;
  
  CREATE POLICY "Admins/Trainers can manage questions"
    ON questions
    FOR ALL
    TO public
    USING (
      organization_id = get_my_organization_id() 
      AND get_my_role() IN ('admin', 'trainer')
    );
    
  CREATE POLICY "Users can view questions in their org"
    ON questions
    FOR SELECT
    TO public
    USING (organization_id = get_my_organization_id());

  -- Update tests policies
  DROP POLICY IF EXISTS "Admins/Trainers can manage tests" ON tests;
  DROP POLICY IF EXISTS "Users can view tests in their org" ON tests;
  
  CREATE POLICY "Admins/Trainers can manage tests"
    ON tests
    FOR ALL
    TO public
    USING (
      organization_id = get_my_organization_id() 
      AND get_my_role() IN ('admin', 'trainer')
    );
    
  CREATE POLICY "Users can view tests in their org"
    ON tests
    FOR SELECT
    TO public
    USING (organization_id = get_my_organization_id());

  -- Update test_questions policies
  DROP POLICY IF EXISTS "Admins/Trainers can manage test_questions" ON test_questions;
  DROP POLICY IF EXISTS "Users can view test_questions if they can view the test" ON test_questions;
  
  CREATE POLICY "Admins/Trainers can manage test_questions"
    ON test_questions
    FOR ALL
    TO public
    USING (
      EXISTS (
        SELECT 1 FROM tests 
        WHERE tests.id = test_questions.test_id 
        AND tests.organization_id = get_my_organization_id()
        AND get_my_role() IN ('admin', 'trainer')
      )
    );
    
  CREATE POLICY "Users can view test_questions if they can view the test"
    ON test_questions
    FOR SELECT
    TO public
    USING (
      EXISTS (
        SELECT 1 FROM tests 
        WHERE tests.id = test_questions.test_id 
        AND tests.organization_id = get_my_organization_id()
      )
    );

  -- Update test_results policies
  DROP POLICY IF EXISTS "Admins/Trainers can view results in their org" ON test_results;
  DROP POLICY IF EXISTS "Users can view their own test results" ON test_results;
  DROP POLICY IF EXISTS "Candidates can create their own test results" ON test_results;
  
  CREATE POLICY "Admins/Trainers can view results in their org"
    ON test_results
    FOR SELECT
    TO public
    USING (
      EXISTS (
        SELECT 1 FROM profiles 
        WHERE profiles.id = test_results.user_id 
        AND profiles.organization_id = get_my_organization_id()
      ) 
      AND get_my_role() IN ('admin', 'trainer')
    );
    
  CREATE POLICY "Users can view their own test results"
    ON test_results
    FOR SELECT
    TO public
    USING (user_id = auth.uid());
    
  CREATE POLICY "Candidates can create their own test results"
    ON test_results
    FOR INSERT
    TO public
    WITH CHECK (user_id = auth.uid());
END $$;